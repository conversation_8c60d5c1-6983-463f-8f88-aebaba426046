import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ContractDocumentPreview from './ContractDocumentPreview';
import WizardStepper from './WizardStepper';
import { ContractWizardProvider, useContractWizard } from './ContractWizardContext';
import { useContractWizardValidation } from './useContractWizardValidation';
import UnifiedContractPreviewModal from '@/components/modals/UnifiedContractPreviewModal';
import SaveAsTemplateModal from './SaveAsTemplateModal';
import Step1JurisdictionType from './Step1JurisdictionType';
import { default as Step2PartiesInfo } from './Step2PartiesInfo';
import Step3ContractTerms from './Step3ContractTerms';
import Step4LegalClauses from './Step4LegalClauses';
import Step5IndustrySpecific from './Step5IndustrySpecific';
import Step6Attachments from './Step6Attachments';
import Step7ReviewApproval from './Step7ReviewApproval';
import AIAnalysisInterface from './AIAnalysisInterface';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CheckCircle, Save, ArrowLeft, FileText, Loader2, Brain, BookTemplate } from 'lucide-react';
import { SimpleLoadingOverlay } from '@/components/ui/loading';
import { useToast } from '@/components/ui/use-toast';

// Step names for better UX (imported from WizardStepper)
const stepNames = [
  'Jurisdiction',
  'Parties',
  'Terms',
  'Clauses',
  'Industry',
  'Attachments',
  'Review & Approval'
];

const StepPanel: React.FC = () => {
  const { currentStep } = useContractWizard();
  switch (currentStep) {
    case 0:
      return <Step1JurisdictionType />;
    case 1:
      return <Step2PartiesInfo />;
    case 2:
      return <Step3ContractTerms />;
    case 3:
      return <Step4LegalClauses />;
    case 4:
      return <Step5IndustrySpecific />;
    case 5:
      return <Step6Attachments />;
    case 6:
      return <Step7ReviewApproval />;
    // ... add more cases for other steps
    default:
      return null;
  }
};

const DraftPrompt: React.FC = () => {
  const { loadDraft } = useContractWizard();
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (localStorage.getItem('contract-wizard-draft')) {
      setShow(true);
    }
  }, []);

  if (!show) return null;
  return (
    <Alert className="mb-2 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800 py-1.5 px-3">
      <AlertDescription className="flex items-center justify-between text-xs">
        <span className="text-amber-800 dark:text-amber-200">Resume your saved draft?</span>
        <div className="flex gap-1.5">
          <Button
            variant="outline"
            size="sm"
            className="h-6 px-2 text-xs bg-amber-100 border-amber-200 text-amber-800 hover:bg-amber-200 hover:text-amber-900 dark:bg-amber-900 dark:border-amber-700 dark:text-amber-200 dark:hover:bg-amber-800"
            onClick={() => { loadDraft(); setShow(false); }}
          >
            Load Draft
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs text-amber-700 hover:text-amber-900 hover:bg-amber-100 dark:text-amber-300 dark:hover:bg-amber-900"
            onClick={() => setShow(false)}
          >
            Dismiss
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};

// New shell component that consumes the context
const ContractWizardShell: React.FC = () => {
  const navigate = useNavigate();
  const { setData, saveDraft } = useContractWizard();
  const {
    currentStep,
    setCurrentStep,
    isStepValid,
    isStepComplete,
    canProceed,
    goToNextStep,
    goToPreviousStep,
    saveContract,
    isLoading,
    error
  } = useContractWizardValidation();
  const { toast } = useToast();
  const [showSaved, setShowSaved] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [templateModalOpen, setTemplateModalOpen] = useState(false);
  const [mobileView, setMobileView] = useState<number>(-1); // -1 for preview, 0+ for wizard steps
  const [isSaving, setIsSaving] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);

  // Sync mobile view with current step when it changes
  useEffect(() => {
    if (mobileView !== -1) {
      setMobileView(currentStep);
    }
  }, [currentStep]);

  // Check for imported document data
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const isImported = searchParams.get('imported') === 'true';

    if (isImported) {
      try {
        const importedData = sessionStorage.getItem('importedContractData');
        if (importedData) {
          const parsedData = JSON.parse(importedData);

          // Update contract data with imported information
          setData(prevData => ({
            ...prevData,
            title: parsedData.title || 'Imported Contract',
            description: parsedData.content?.substring(0, 200) + '...' || '',
            parties: parsedData.parties?.length ?
              parsedData.parties.map((party: any) => ({
                type: party.type || 'organization',
                name: party.name || '',
                address: party.address || '',
                representative: party.representative || '',
                title: party.title || '',
                role: party.role || ''
              })) :
              prevData.parties,
            effectiveDate: parsedData.effectiveDate || new Date().toISOString().split('T')[0],
            contractType: parsedData.type || 'agreement',
            // Store the full content for the document preview
            importedContent: parsedData.content
          }));

          // Clear the session storage
          sessionStorage.removeItem('importedContractData');
        }
      } catch (error) {
        console.error('Error loading imported contract data:', error);
      }
    }
  }, [setData]);

  // Listen for AI Assistant events from document preview
  useEffect(() => {
    const handleOpenAIAssistant = () => {
      setShowAIModal(true);
    };

    window.addEventListener('openAIAssistant', handleOpenAIAssistant);
    return () => {
      window.removeEventListener('openAIAssistant', handleOpenAIAssistant);
    };
  }, []);

  const handleSaveDraft = () => {
    saveDraft();
    setShowSaved(true);
    setTimeout(() => setShowSaved(false), 2000);
  };

  const handleSaveContract = async () => {
    setIsSaving(true);
    try {
      const contractId = await saveContract();
      if (contractId) {
        toast({
          title: "Contract Saved",
          description: "Your contract has been saved successfully.",
        });
        // Navigate to the contract view page
        navigate(`/contracts/${contractId}`);
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to save contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen w-full bg-background p-3 sm:p-4 md:p-5 relative">
      {/* Loading Overlay */}
      {(isLoading || isSaving) && <SimpleLoadingOverlay text="Processing contract..." />}

      {/* AI Assistant Modal */}
      <Dialog open={showAIModal} onOpenChange={setShowAIModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              AI-Powered Document Analysis
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto">
            <AIAnalysisInterface />
          </div>
        </DialogContent>
      </Dialog>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Contract Preview Modal */}
      <UnifiedContractPreviewModal
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        contractId={null}
        showActions={false}
      />

      {/* Save as Template Modal */}
      <SaveAsTemplateModal open={templateModalOpen} onOpenChange={setTemplateModalOpen} />

      {/* Mobile Tabs for switching between preview and wizard */}
      <div className="flex xl:hidden mb-4 border-b">
        <Button
          variant={mobileView === -1 ? "default" : "ghost"}
          className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary"
          data-state={mobileView === -1 ? "active" : "inactive"}
          onClick={() => setMobileView(-1)}
        >
          Preview
        </Button>
        <Button
          variant={mobileView !== -1 ? "default" : "ghost"}
          className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary"
          data-state={mobileView !== -1 ? "active" : "inactive"}
          onClick={() => mobileView === -1 && setMobileView(currentStep)}
        >
          Wizard
        </Button>
      </div>

      <div className="flex flex-col xl:flex-row w-full gap-4 sm:gap-5">
        {/* Left: Contract Preview/Editor - Hidden on mobile when wizard is active */}
        <div className={`flex-1 ${mobileView !== -1 ? "hidden xl:block" : ""}`}>
          <Card className="h-full shadow-sm">
            <CardHeader className="pb-2 pt-3 px-3 sm:px-4 flex flex-row items-center justify-between">
              <CardTitle>Contract Preview</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileView(currentStep)}
                className="h-8 xl:hidden"
              >
                Edit
              </Button>
            </CardHeader>
            <CardContent className="pt-0 px-3 sm:px-4 pb-4">
              <ContractDocumentPreview />
            </CardContent>
          </Card>
        </div>

        {/* Right: Wizard Steps - Full width on mobile when wizard is active */}
        <div className={`w-full xl:w-[400px] shrink-0 ${mobileView === -1 ? "hidden xl:block" : ""}`}>
          <Card className="h-full shadow-sm flex flex-col border-slate-200 dark:border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 pt-3 sm:pt-4 px-3 sm:px-5 border-b dark:border-slate-700">
              <div className="flex flex-col space-y-0">
                <CardTitle className="text-lg sm:text-xl font-semibold">{stepNames[currentStep]}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setMobileView(-1)}
                  className="h-8 xl:hidden hover:bg-slate-100 dark:hover:bg-slate-800"
                >
                  <FileText className="h-3.5 w-3.5 mr-1.5" />
                  Preview
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/contracts')}
                  className="h-8 hover:bg-slate-100 dark:hover:bg-slate-800"
                >
                  <ArrowLeft className="h-3.5 w-3.5 mr-1.5" />
                  Back
                </Button>
              </div>
            </CardHeader>

            <div className="px-3 sm:px-5 py-3 border-b bg-slate-50/50 dark:bg-slate-900/50 flex flex-wrap items-center justify-between gap-2">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSaveDraft}
                  className="h-8 bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
                >
                  <Save className="h-3.5 w-3.5 mr-1.5" />
                  <span className="hidden xs:inline">Save</span> Draft
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewOpen(true)}
                  className="h-8 bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
                >
                  <FileText className="h-3.5 w-3.5 mr-1.5" />
                  Preview
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTemplateModalOpen(true)}
                  className="h-8 bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
                >
                  <BookTemplate className="h-3.5 w-3.5 mr-1.5" />
                  <span className="hidden sm:inline">Save as</span> Template
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSaveContract}
                  disabled={isSaving}
                  className="h-8"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                      Save Contract
                    </>
                  )}
                </Button>
              </div>

              {showSaved && (
                <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1 bg-green-50 dark:bg-green-900/30 px-2 py-1 rounded-full">
                  <CheckCircle className="h-3 w-3" />
                  Saved
                </span>
              )}
            </div>

            <DraftPrompt />

            <CardContent className="px-3 sm:px-5 pt-4 pb-0 h-[calc(100vh-12rem)] sm:h-[calc(100vh-10rem)] overflow-y-auto">
              <WizardStepper />

              <div className="mt-6 pb-20">
                <StepPanel />

                <div className="mt-8 flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPreviousStep}
                    className="h-8 bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
                  >
                    <ArrowLeft className="h-3.5 w-3.5 mr-1.5" />
                    Back
                  </Button>

                  {currentStep < 6 ? (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={goToNextStep}
                      disabled={!canProceed}
                      className="h-8"
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleSaveContract}
                      disabled={isSaving}
                      className="h-8"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                          Complete
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </div>
    </div>
  );
};

// Top-level component only provides the context
const ContractWizard: React.FC = () => {
  return (
    <ContractWizardProvider>
      <ContractWizardShell />
    </ContractWizardProvider>
  );
};

export default ContractWizard;